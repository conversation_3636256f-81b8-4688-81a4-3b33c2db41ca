import axios from 'axios'

/**
 * API 接口管理类
 * 基于 axios 封装，提供统一的请求配置和拦截器
 */
class ApiClient {
  constructor(config = {}) {
    // 默认配置
    const defaultConfig = {
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://206.233.245.198/LoadData',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    }

    // 合并配置
    this.config = { ...defaultConfig, ...config }
    
    // 创建 axios 实例
    this.instance = axios.create(this.config)
    
    // 设置拦截器
    this.setupInterceptors()
  }

  /**
   * 设置请求和响应拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        console.log('🚀 发送请求:', config.method?.toUpperCase(), config.url)

        // 检查是否有缓存的用户数据
        const userInfo = this.getUserInfo()
        const token = this.getToken()

        // 如果有用户信息或token，添加认证头
        if (userInfo || token) {
          // 添加用户UID到请求头
          if (userInfo && userInfo.uid) {
            config.headers['X-Login-UID'] = userInfo.uid
            console.log('添加用户UID到请求头:', userInfo.uid)
          }

          // 优先使用用户信息中的token，其次使用单独存储的token
          const authToken = (userInfo && userInfo.token) || token
          if (authToken) {
            config.headers['X-Auth-Token'] = authToken
            config.headers.Authorization = `Bearer ${authToken}`
            console.log('添加认证Token到请求头')
          }
        }

        return config
      },
      (error) => {
        console.error('❌ 请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        console.log('✅ 响应成功:', response.config.url, response.status)

        const { data } = response

        // 处理服务器返回的标准格式: { code, msg, data }
        if (data && typeof data === 'object' && 'code' in data) {
          if (data.code === 0) {
            // 成功响应（code: 0 表示成功），返回 data 字段
            return data.data || data
          } else {
            // 业务错误，抛出错误并附带 code 信息
            const errorMessage = data.msg || '请求失败'
            console.error('❌ 业务错误:', data.code, errorMessage)

            // 创建错误对象，包含 code 和 message
            const error = new Error(errorMessage)
            error.code = data.code
            error.serverResponse = data
            throw error
          }
        }

        // 如果不是标准格式，直接返回原数据
        return data
      },
      (error) => {
        console.error('❌ 响应错误:', error)
        this.handleError(error)
        return Promise.reject(error)
      }
    )
  }

  /**
   * 处理错误响应
   */
  handleError(error) {
    let errorMessage = '网络请求失败'
    let shouldRedirectToLogin = false

    // 检查是否是业务逻辑错误（从响应拦截器抛出的错误）
    if (error.code && error.serverResponse) {
      const { code, msg } = error.serverResponse
      errorMessage = msg || '请求失败'

      // 根据业务错误码进行特殊处理
      switch (code) {
        case 1:
          // 登录失败，账号或密码错误
          console.log('登录失败:', errorMessage)
          break
        case 403:
          // 需要登录
          shouldRedirectToLogin = true
          break
        default:
          console.log('其他业务错误:', code, errorMessage)
      }
    } else if (error.response) {
      // HTTP 错误
      const { status, data } = error.response

      // 优先使用服务器返回的 msg 字段
      if (data && data.msg) {
        errorMessage = data.msg

        // 检查业务错误码
        if (data.code === 403) {
          shouldRedirectToLogin = true
        }
      } else {
        // 根据 HTTP 状态码处理
        switch (status) {
          case 400:
            errorMessage = '请求参数错误'
            break
          case 401:
            errorMessage = '未授权，请重新登录'
            shouldRedirectToLogin = true
            break
          case 403:
            errorMessage = '拒绝访问'
            shouldRedirectToLogin = true
            break
          case 404:
            errorMessage = '请求的资源不存在'
            break
          case 500:
            errorMessage = '服务器内部错误'
            break
          default:
            errorMessage = `请求失败 (${status})`
        }
      }
    } else if (error.request) {
      errorMessage = '网络连接超时，请检查网络'
    } else {
      errorMessage = error.message || '请求配置错误'
    }

    // 使用 Vant 4 Toast 显示错误信息
    console.log('准备显示 Toast 错误信息:', errorMessage)
    try {
      // 动态导入 showFailToast
      import('vant').then(({ showFailToast }) => {
        showFailToast({
          message: errorMessage,
          duration: 3000
        })
        console.log('Toast 显示成功')
      }).catch((importError) => {
        console.error('Toast 导入失败:', importError)
        alert(errorMessage)
      })
    } catch (toastError) {
      console.error('Toast 显示失败:', toastError)
      // 降级到 alert
      alert(errorMessage)
    }

    // 如果需要跳转到登录页
    if (shouldRedirectToLogin) {
      this.handleUnauthorized()
    }
  }

  /**
   * 处理未授权情况
   */
  handleUnauthorized() {
    // 清除登录缓存
    this.clearLoginCache()

    // 跳转到登录页面
    if (typeof window !== 'undefined' && window.location) {
      window.location.href = '/'
    }
  }

  /**
   * 获取存储的 token
   */
  getToken() {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('access_token')
    }
    return null
  }

  /**
   * 设置 token
   */
  setToken(token) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token)
    }
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (typeof window !== 'undefined') {
      const userInfo = localStorage.getItem('user_info')
      return userInfo ? JSON.parse(userInfo) : null
    }
    return null
  }

  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user_info', JSON.stringify(userInfo))
    }
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!this.getToken()
  }

  /**
   * 清除登录缓存
   */
  clearLoginCache() {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_info')
      console.log('登录缓存已清除')
    }
  }

  // HTTP 方法封装
  get(url, params = {}, config = {}) {
    return this.instance.get(url, { params, ...config })
  }

  post(url, data = {}, config = {}) {
    return this.instance.post(url, data, config)
  }

  put(url, data = {}, config = {}) {
    return this.instance.put(url, data, config)
  }

  delete(url, config = {}) {
    return this.instance.delete(url, config)
  }

  // ========== 业务接口 ==========

  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名 (必填)
   * @param {string} loginData.password - 密码 (必填)
   * @returns {Promise} 登录结果
   */
  login(loginData) {
    return this.post('/login', loginData)
  }

  /**
   * 用户注销
   * @returns {Promise} 注销结果
   */
  logout() {
    // 直接清除本地缓存，不调用服务器接口
    this.clearLoginCache()
    return Promise.resolve({ success: true, message: '注销成功' })
  }

  /**
   * 加载应用配置
   * @returns {Promise} 配置信息
   */
  loadConfig() {
    return this.post('/config')
  }

  /**
   * 加载分类数据
   * @returns {Promise} 分类信息
   */
  loadSorts() {
    return this.post('/sorts')
  }

  /**
   * 获取缓存的配置信息
   * @returns {Object|null} 配置信息
   */
  getConfig() {
    try {
      const config = localStorage.getItem('app_config')
      return config ? JSON.parse(config) : null
    } catch (error) {
      console.error('获取配置信息失败:', error)
      return null
    }
  }

  /**
   * 缓存配置信息
   * @param {Object} config 配置信息
   */
  setConfig(config) {
    try {
      localStorage.setItem('app_config', JSON.stringify(config))
      console.log('配置信息已缓存:', config)
    } catch (error) {
      console.error('缓存配置信息失败:', error)
    }
  }

  /**
   * 清除配置缓存
   */
  clearConfig() {
    try {
      localStorage.removeItem('app_config')
      console.log('配置缓存已清除')
    } catch (error) {
      console.error('清除配置缓存失败:', error)
    }
  }

  /**
   * 测试请求头（开发环境使用）
   */
  testHeaders() {
    if (import.meta.env.DEV) {
      console.group('🔍 请求头测试')

      const userInfo = this.getUserInfo()
      const token = this.getToken()
      const isLoggedIn = this.isLoggedIn()

      console.log('用户信息:', userInfo)
      console.log('Token:', token)
      console.log('登录状态:', isLoggedIn)

      if (userInfo || token) {
        console.log('预期请求头:')
        if (userInfo && userInfo.uid) {
          console.log('  X-Login-UID:', userInfo.uid)
        }
        const authToken = (userInfo && userInfo.token) || token
        if (authToken) {
          console.log('  X-Auth-Token:', authToken)
          console.log('  Authorization:', `Bearer ${authToken}`)
        }
      } else {
        console.log('无认证信息，不会添加请求头')
      }

      console.groupEnd()
    }
  }
}

// 创建默认实例
const api = new ApiClient()

// 导出类和默认实例
export { ApiClient }
export default api
