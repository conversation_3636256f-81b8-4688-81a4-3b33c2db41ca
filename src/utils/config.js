import api from './api'

/**
 * 应用配置管理类
 * 负责加载、缓存和管理应用配置信息
 */
class ConfigManager {
  constructor() {
    this.config = null
    this.isLoading = false
    this.loadPromise = null
  }

  /**
   * 初始化配置
   * 优先从缓存加载，如果缓存不存在则从服务器加载
   * @returns {Promise<Object>} 配置信息
   */
  async init() {
    // 如果正在加载中，返回加载Promise
    if (this.isLoading && this.loadPromise) {
      return this.loadPromise
    }

    // 如果已经有配置，直接返回
    if (this.config) {
      return this.config
    }

    // 先尝试从缓存加载
    const cachedConfig = api.getConfig()
    if (cachedConfig) {
      this.config = cachedConfig
      console.log('从缓存加载配置:', this.config)
      
      // 异步更新配置（不阻塞当前流程）
      this.updateConfigInBackground()
      
      return this.config
    }

    // 缓存不存在，从服务器加载
    return this.loadFromServer()
  }

  /**
   * 从服务器加载配置
   * @returns {Promise<Object>} 配置信息
   */
  async loadFromServer() {
    if (this.isLoading && this.loadPromise) {
      return this.loadPromise
    }

    this.isLoading = true
    this.loadPromise = this._fetchConfig()

    try {
      const config = await this.loadPromise
      this.config = config
      
      // 缓存配置信息
      api.setConfig(config)
      
      console.log('从服务器加载配置成功:', config)
      return config
    } catch (error) {
      console.error('加载配置失败:', error)
      
      // 如果加载失败，使用默认配置
      const defaultConfig = this.getDefaultConfig()
      this.config = defaultConfig
      
      console.log('使用默认配置:', defaultConfig)
      return defaultConfig
    } finally {
      this.isLoading = false
      this.loadPromise = null
    }
  }

  /**
   * 后台更新配置（不阻塞当前流程）
   */
  async updateConfigInBackground() {
    try {
      console.log('后台更新配置...')
      const newConfig = await this._fetchConfig()
      
      // 比较配置是否有变化
      if (JSON.stringify(newConfig) !== JSON.stringify(this.config)) {
        this.config = newConfig
        api.setConfig(newConfig)
        console.log('配置已更新:', newConfig)
        
        // 可以在这里触发配置更新事件
        this.notifyConfigUpdate(newConfig)
      } else {
        console.log('配置无变化')
      }
    } catch (error) {
      console.warn('后台更新配置失败:', error)
    }
  }

  /**
   * 实际的配置获取方法
   * @returns {Promise<Object>} 配置信息
   */
  async _fetchConfig() {
    const response = await api.loadConfig()
    
    // 验证响应格式
    if (!response || typeof response !== 'object') {
      throw new Error('配置响应格式无效')
    }

    return response
  }

  /**
   * 获取默认配置
   * @returns {Object} 默认配置
   */
  getDefaultConfig() {
    return {
      app_name: 'US Shop',
      version: '1.0.0',
      api_timeout: 10000,
      max_retry: 3,
      features: {
        cart_enabled: true,
        payment_enabled: true,
        statistics_enabled: true
      },
      ui: {
        theme: 'default',
        language: 'en',
        currency: 'USD'
      },
      loaded_at: new Date().toISOString(),
      source: 'default'
    }
  }

  /**
   * 获取当前配置
   * @returns {Object|null} 当前配置
   */
  getConfig() {
    return this.config
  }

  /**
   * 获取配置项
   * @param {string} key 配置键，支持点号分隔的嵌套键
   * @param {*} defaultValue 默认值
   * @returns {*} 配置值
   */
  get(key, defaultValue = null) {
    if (!this.config) {
      return defaultValue
    }

    const keys = key.split('.')
    let value = this.config

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return defaultValue
      }
    }

    return value
  }

  /**
   * 检查功能是否启用
   * @param {string} feature 功能名称
   * @returns {boolean} 是否启用
   */
  isFeatureEnabled(feature) {
    return this.get(`features.${feature}`, false)
  }

  /**
   * 强制重新加载配置
   * @returns {Promise<Object>} 配置信息
   */
  async reload() {
    this.config = null
    api.clearConfig()
    return this.loadFromServer()
  }

  /**
   * 通知配置更新（可以扩展为事件系统）
   * @param {Object} newConfig 新配置
   */
  notifyConfigUpdate(newConfig) {
    // 可以在这里触发全局事件或调用回调函数
    console.log('配置已更新，可以在这里通知其他组件')
    
    // 示例：可以触发自定义事件
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('configUpdated', {
        detail: { config: newConfig }
      }))
    }
  }

  /**
   * 获取配置状态信息
   * @returns {Object} 状态信息
   */
  getStatus() {
    return {
      hasConfig: !!this.config,
      isLoading: this.isLoading,
      loadedAt: this.config?.loaded_at || null,
      source: this.config?.source || null
    }
  }
}

// 创建全局配置管理实例
const configManager = new ConfigManager()

// 导出配置管理器和便捷方法
export default configManager

// 导出便捷方法
export const initConfig = () => configManager.init()
export const getConfig = (key, defaultValue) => configManager.get(key, defaultValue)
export const isFeatureEnabled = (feature) => configManager.isFeatureEnabled(feature)
export const reloadConfig = () => configManager.reload()
export const getConfigStatus = () => configManager.getStatus()
