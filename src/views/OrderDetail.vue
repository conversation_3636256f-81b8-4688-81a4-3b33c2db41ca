<template>
  <div class="order-detail-page">
    <!-- 顶部导航 -->
    <div class="header">
      <div class="header-left" @click="goBack">
        <van-icon name="arrow-left" color="#1a1a1a" size="20" />
      </div>
      <h2 class="header-title">订单详情</h2>
      <div class="header-right"></div>
    </div>

    <!-- 订单详情弹窗 -->
    <van-popup
      v-model:show="showOrderDetail"
      position="bottom"
      :style="{ height: '85%' }"
      round
      closeable
      close-icon="cross"
      @close="goBack"
      @opened="onPopupOpened"
    >
      <div class="order-detail-popup">
        <!-- 订单详情头部 -->
        <div class="order-detail-header">
          <h3 class="order-detail-title">订单详情</h3>
          <div class="order-number">
            <span class="order-id">#{{ orderData.orderNumber || 'NEW-ORDER' }}</span>
            <span class="order-date">{{ orderData.date || getCurrentDate() }}</span>
          </div>
        </div>

        <!-- 订单详情内容 -->
        <div class="order-detail-content">
          <!-- 订单摘要 -->
          <div class="order-summary-section">
            <h4 class="section-title">订单摘要</h4>
            <div class="order-items">
              <div
                v-for="item in orderData.items"
                :key="item.id"
                class="order-item"
              >
                <div class="order-item-image">
                  <img :src="item.image" :alt="item.name" />
                  <div class="order-item-badge" v-if="item.discount">
                    -{{ item.discount }}%
                  </div>
                </div>
                <div class="order-item-info">
                  <div class="order-item-content">
                    <h5 class="order-item-name">{{ item.name }}</h5>
                    <p class="order-item-description">{{ item.description || '新鲜优质商品' }}</p>
                  </div>
                  <div class="order-item-footer">
                    <div class="order-item-left">
                      <div class="order-item-price">
                        <span class="current-price">${{ item.price }}</span>
                        <span v-if="item.originalPrice" class="original-price">
                          ${{ item.originalPrice }}
                        </span>
                      </div>
                    </div>
                    <div class="order-item-quantity">
                      <span class="quantity-text">数量: {{ item.quantity }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 业务员信息 -->
          <div class="salesman-section">
            <h4 class="section-title">业务员信息</h4>
            <div class="salesman-info">
              <div class="salesman-avatar">
                <van-icon name="contact" color="#7ed321" size="24" />
              </div>
              <div class="salesman-details">
                <span class="salesman-name">{{ salesmanInfo.name }}</span>
                <span class="salesman-phone">{{ salesmanInfo.phone }}</span>
              </div>
            </div>
          </div>

          <!-- 支付方式 -->
          <div class="payment-section">
            <h4 class="section-title">支付方式</h4>
            <div class="payment-method">
              <div class="payment-option selected">
                <div class="payment-left">
                  <van-icon 
                    :name="getPaymentIcon(paymentMethod)" 
                    :color="getPaymentColor(paymentMethod)" 
                    size="20" 
                  />
                  <div class="payment-info">
                    <span class="payment-name">{{ getPaymentName(paymentMethod) }}</span>
                    <span class="payment-desc">{{ getPaymentDesc(paymentMethod) }}</span>
                  </div>
                </div>
                <van-icon name="success" color="#7ed321" size="20" />
              </div>
            </div>
          </div>

          <!-- 价格明细 -->
          <div class="price-section">
            <h4 class="section-title">价格明细</h4>
            <div class="price-details">
              <div class="price-row">
                <span class="price-label">商品小计</span>
                <span class="price-value">${{ subtotal.toFixed(2) }}</span>
              </div>
              <div class="price-row">
                <span class="price-label">配送费</span>
                <span class="price-value">${{ deliveryFee.toFixed(2) }}</span>
              </div>
              <div class="price-row">
                <span class="price-label">税费</span>
                <span class="price-value">${{ tax.toFixed(2) }}</span>
              </div>
              <div class="price-row total-row">
                <span class="price-label">总计</span>
                <span class="price-value total-price">${{ total.toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 订单详情底部 -->
        <div class="order-detail-footer" v-if="shouldShowFooterButtons">
          <button
            class="cancel-btn"
            @click="cancelOrder"
            v-if="canCancelOrder"
          >
            取消订单
          </button>
          <button
            class="pay-btn"
            @click="payOrder"
            v-if="canPayOrder"
            :class="{ 'full-width': !canCancelOrder }"
          >
            {{ getPayButtonText() }}
          </button>
        </div>

        <!-- 订单状态信息（当不能操作时显示） -->
        <div class="order-status-footer" v-if="!shouldShowFooterButtons">
          <div class="status-info">
            <van-icon
              :name="getStatusIcon()"
              :color="getStatusColor()"
              size="24"
            />
            <div class="status-text">
              <span class="status-label">订单状态</span>
              <span class="status-value">{{ getStatusText() }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showConfirmDialog, showToast } from 'vant'

const router = useRouter()
const route = useRoute()

// 响应式数据
const showOrderDetail = ref(false)
const isFromCart = ref(false) // 是否来自购物车

// 订单数据
const orderData = ref({
  orderNumber: '',
  date: '',
  items: [],
  total: 0,
  status: 'Pending' // 添加订单状态
})

// 业务员信息
const salesmanInfo = ref({
  name: 'John Smith',
  phone: '+****************'
})

// 支付方式
const paymentMethod = ref('card')

// 价格相关
const deliveryFee = ref(5.99)
const tax = ref(0)

// 计算属性
const subtotal = computed(() => {
  return orderData.value.items.reduce((total, item) => total + (item.price * item.quantity), 0)
})

const total = computed(() => {
  const taxAmount = subtotal.value * 0.08 // 8% 税率
  tax.value = taxAmount
  return subtotal.value + deliveryFee.value + taxAmount
})

// 订单状态相关计算属性
const canCancelOrder = computed(() => {
  const cancelableStatuses = ['Pending', 'Processing']
  return isFromCart.value || cancelableStatuses.includes(orderData.value.status)
})

const canPayOrder = computed(() => {
  const payableStatuses = ['Pending', 'Processing']
  return isFromCart.value || payableStatuses.includes(orderData.value.status)
})

const shouldShowFooterButtons = computed(() => {
  return canCancelOrder.value || canPayOrder.value
})

// 获取当前日期
const getCurrentDate = () => {
  const now = new Date()
  return now.toISOString().split('T')[0]
}

// 支付方式相关方法
const getPaymentIcon = (method) => {
  const icons = {
    card: 'credit-card',
    paypal: 'cash-back-record',
    cashapp: 'cash-back-record',
    all: 'credit-card'
  }
  return icons[method] || 'credit-card'
}

const getPaymentColor = (method) => {
  const colors = {
    card: '#1a73e8',
    paypal: '#0070ba',
    cashapp: '#00d632',
    all: '#1a73e8'
  }
  return colors[method] || '#1a73e8'
}

const getPaymentName = (method) => {
  const names = {
    card: '信用卡支付',
    paypal: 'PayPal',
    cashapp: 'Cash App',
    all: '信用卡支付'
  }
  return names[method] || '信用卡支付'
}

const getPaymentDesc = (method) => {
  const descs = {
    card: '使用信用卡或借记卡支付',
    paypal: '使用PayPal账户支付',
    cashapp: '使用Cash App支付',
    all: '使用信用卡或借记卡支付'
  }
  return descs[method] || '使用信用卡或借记卡支付'
}

// 订单状态相关方法
const getPayButtonText = () => {
  if (isFromCart.value) {
    return '立即支付'
  }

  switch (orderData.value.status) {
    case 'Pending':
      return '立即支付'
    case 'Processing':
      return '重新支付'
    default:
      return '支付'
  }
}

const getStatusIcon = () => {
  switch (orderData.value.status) {
    case 'Delivered':
      return 'success'
    case 'Cancelled':
      return 'cross'
    case 'Processing':
      return 'clock-o'
    default:
      return 'pending-payment'
  }
}

const getStatusColor = () => {
  switch (orderData.value.status) {
    case 'Delivered':
      return '#7ed321'
    case 'Cancelled':
      return '#e74c3c'
    case 'Processing':
      return '#f39c12'
    default:
      return '#999999'
  }
}

const getStatusText = () => {
  const statusMap = {
    'Pending': '待支付',
    'Processing': '处理中',
    'Delivered': '已送达',
    'Cancelled': '已取消'
  }
  return statusMap[orderData.value.status] || '未知状态'
}

// 页面方法
const goBack = () => {
  showOrderDetail.value = false
  setTimeout(() => {
    router.back()
  }, 300)
}

const onPopupOpened = () => {
  console.log('订单详情弹窗已打开')
}

const cancelOrder = () => {
  showConfirmDialog({
    title: '取消订单',
    message: '确定要取消这个订单吗？',
    confirmButtonText: '确定取消',
    cancelButtonText: '继续支付',
    confirmButtonColor: '#e74c3c',
  })
  .then(() => {
    showToast('订单已取消')
    goBack()
  })
  .catch(() => {
    console.log('用户选择继续支付')
  })
}

const payOrder = () => {
  showToast('正在跳转到支付页面...')
  // 这里可以添加支付逻辑
  setTimeout(() => {
    showToast('支付成功！')
    goBack()
  }, 2000)
}

// 组件挂载时的逻辑
onMounted(() => {
  // 从路由参数获取数据
  const { orderData: routeOrderData, isFromCart: routeIsFromCart, paymentMethod: routePaymentMethod, salesmanInfo: routeSalesmanInfo } = route.query

  if (routeOrderData) {
    try {
      const parsedOrderData = JSON.parse(routeOrderData)
      orderData.value = {
        ...parsedOrderData,
        status: parsedOrderData.status || 'Pending' // 确保有状态字段
      }
    } catch (error) {
      console.error('解析订单数据失败:', error)
    }
  }

  if (routeIsFromCart) {
    isFromCart.value = routeIsFromCart === 'true'
  }

  if (routePaymentMethod) {
    paymentMethod.value = routePaymentMethod
  }

  if (routeSalesmanInfo) {
    try {
      salesmanInfo.value = JSON.parse(routeSalesmanInfo)
    } catch (error) {
      console.error('解析业务员信息失败:', error)
    }
  }

  // 显示弹窗
  showOrderDetail.value = true
})
</script>

<style scoped>
/* 页面容器 */
.order-detail-page {
  width: 100%;
  height: 100vh;
  background: #f8f9fa;
  position: relative;
}

/* 顶部导航 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  cursor: pointer;
  padding: 8px;
  margin-left: -8px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-right {
  width: 36px;
}

/* 订单详情弹窗样式 */
.order-detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.order-detail-header {
  padding: 20px 16px 16px 16px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.order-detail-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.order-number {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-id {
  font-size: 14px;
  font-weight: 600;
  color: #7ed321;
}

.order-date {
  font-size: 12px;
  color: #999999;
}

.order-detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 各个区块样式 */
.order-summary-section,
.salesman-section,
.payment-section,
.price-section {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12px;
}

/* 订单商品列表样式 */
.order-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: flex;
  flex-direction: row;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.order-item-image {
  position: relative;
  width: 100px;
  height: 100%;
  flex-shrink: 0;
  overflow: hidden;
}

.order-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.order-item-badge {
  position: absolute;
  top: 4px;
  left: 4px;
  background: #e74c3c;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.order-item-info {
  flex: 1;
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.order-item-content {
  flex: 1;
}

.order-item-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
  line-height: 1.3;
}

.order-item-description {
  font-size: 12px;
  color: #999999;
  margin: 0;
  line-height: 1.3;
}

.order-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 8px;
}

.order-item-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.original-price {
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
}

.order-item-quantity {
  display: flex;
  align-items: center;
}

.quantity-text {
  font-size: 12px;
  color: #666666;
  font-weight: 500;
}

/* 业务员信息样式 */
.salesman-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.salesman-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(126, 211, 33, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.salesman-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.salesman-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.salesman-phone {
  font-size: 12px;
  color: #666666;
}

/* 支付方式样式 */
.payment-method {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.payment-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-option.selected {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.05);
}

.payment-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.payment-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.payment-desc {
  font-size: 12px;
  color: #666666;
}

/* 价格明细样式 */
.price-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.price-row:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.price-label {
  font-size: 14px;
  color: #666666;
}

.price-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.total-row {
  border-top: 2px solid #f0f0f0;
  padding-top: 12px;
  margin-top: 4px;
}

.total-row .price-label {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.total-price {
  font-size: 18px;
  font-weight: 700;
  color: #7ed321;
}

/* 底部按钮样式 */
.order-detail-footer {
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
}

.cancel-btn,
.pay-btn {
  flex: 1;
  height: 48px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666666;
  border: 1px solid #e0e0e0;
}

.cancel-btn:hover {
  background: #e9ecef;
}

.pay-btn {
  background: #7ed321;
  color: white;
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.3);
}

.pay-btn:hover {
  background: #6bc91a;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(126, 211, 33, 0.4);
}

.pay-btn:active {
  transform: translateY(0);
}

.pay-btn.full-width {
  flex: 1;
}

/* 订单状态底部样式 */
.order-status-footer {
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
}

.status-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 12px;
}

.status-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.status-label {
  font-size: 12px;
  color: #999999;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

/* 调整Vant Popup关闭按钮的样式 */
:deep(.van-popup__close-icon) {
  top: 16px !important;
  right: 16px !important;
  z-index: 10;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .order-item {
    height: 90px;
  }
  
  .order-item-image {
    width: 90px;
  }
  
  .order-item-info {
    padding: 10px;
  }
  
  .order-item-name {
    font-size: 13px;
  }
  
  .order-item-description {
    font-size: 11px;
  }
  
  .current-price {
    font-size: 13px;
  }
  
  .order-detail-footer {
    padding: 12px 16px;
  }

  .cancel-btn,
  .pay-btn {
    height: 44px;
    font-size: 15px;
  }

  .order-status-footer {
    padding: 12px 16px;
  }

  .status-info {
    padding: 10px;
  }

  .status-label {
    font-size: 11px;
  }

  .status-value {
    font-size: 13px;
  }
}
</style>
