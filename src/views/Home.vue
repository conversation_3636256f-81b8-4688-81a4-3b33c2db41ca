<template>
  <div class="home-container">
    <!-- 分类容器 -->
    <div class="category-section">
      <!-- 标题 -->
      <div class="section-header">
        <h2 class="section-title">Explore Services</h2>
      </div>
      
      <!-- 分类网格容器 -->
      <div class="category-grid-container" :class="{ 'expanded': isExpanded }">
        <!-- 加载状态 -->
        <div v-if="categoriesLoading" class="categories-loading">
          <van-loading color="#7ed321" size="24px">Loading</van-loading>
        </div>

        <!-- 分类网格 -->
        <div v-else class="category-grid">
          <!-- 分类列表 -->
          <div
            v-for="(category, index) in visibleCategories"
            :key="category.id"
            class="category-item"
            @click="handleCategoryClick(category)"
          >
            <div class="category-icon">
              <!-- 如果有分类图片，显示图片；否则显示默认图标 -->
              <img
                v-if="category.imageUrl"
                :src="category.imageUrl"
                :alt="category.name"
                class="category-image"
              />
              <van-icon
                v-else
                :name="category.icon || 'apps-o'"
                :color="category.color || '#7ed321'"
                size="24"
              />
            </div>
            <span class="category-name">{{ category.name }}</span>
          </div>

          <!-- 展开/收起按钮 - 只在需要时显示 -->
          <div
            v-if="shouldShowExpandButton"
            class="category-item expand-item"
            @click="toggleExpand"
          >
            <div class="category-icon expand-icon">
              <van-icon
                :name="isExpanded ? 'arrow-up' : 'arrow-down'"
                color="#7ed321"
                size="20"
              />
            </div>
            <span class="category-name">{{ isExpanded ? 'Less' : 'More' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索栏容器 -->
    <div class="search-section">
      <div class="search-container">
        <van-search
          v-model="searchQuery"
          placeholder="Search for products..."
          show-action
          shape="round"
          background="#ffffff"
          @search="onSearch"
          @cancel="onCancel"
          class="custom-search"
        >
          <template #action>
            <div class="search-action" @click="onSearch">Search</div>
          </template>
        </van-search>
      </div>
    </div>

    <!-- 商品列表容器 -->
    <div class="products-section">
      <!-- 列表头部 -->
      <div class="products-header">
        <h3 class="products-title">Featured Products</h3>
        <div class="layout-toggle">
          <van-icon
            name="bars"
            :color="layoutMode === 'list' ? '#7ed321' : '#999999'"
            size="20"
            @click="setLayoutMode('list')"
            class="layout-icon"
          />
          <van-icon
            name="apps-o"
            :color="layoutMode === 'grid' ? '#7ed321' : '#999999'"
            size="20"
            @click="setLayoutMode('grid')"
            class="layout-icon"
          />
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="products-grid" :class="{ 'list-mode': layoutMode === 'list' }">
        <div
          v-for="product in products"
          :key="product.id"
          class="product-item"
          @click="handleProductClick(product)"
        >
          <div class="product-image">
            <img :src="product.image" :alt="product.name" />
            <div class="product-badge" v-if="product.discount">
              -{{ product.discount }}%
            </div>
          </div>
          <div class="product-info">
            <div class="product-content">
              <h4 class="product-name">{{ product.name }}</h4>
              <p class="product-description">{{ product.description }}</p>
            </div>
            <div class="product-footer">
              <div class="product-left">
                <div class="product-price">
                  <span class="current-price">${{ product.price }}</span>
                  <span v-if="product.originalPrice" class="original-price">
                    ${{ product.originalPrice }}
                  </span>
                </div>
                <div class="product-rating">
                  <van-icon name="star" color="#FFD700" size="12" />
                  <span class="rating-text">{{ product.rating }}</span>
                  <span class="rating-count">({{ product.reviews }})</span>
                </div>
              </div>
              <div class="add-to-cart-btn" @click.stop="addToCart(product, $event)">
                <van-icon name="plus" color="#ffffff" size="16" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 飞入动画小球现在由GSAP动态创建 -->

    <!-- 浮动购物车按钮 -->
    <div
      class="floating-cart"
      @click="goToCart"
      ref="cartRef"
    >
      <div class="cart-icon">
        <van-icon name="shopping-cart-o" color="#ffffff" size="24" />
        <div v-if="cartCount > 0" class="cart-badge">{{ cartCount }}</div>
      </div>
    </div>

    <!-- 底部TabBar -->
    <van-tabbar v-model="activeTab" class="custom-tabbar" @change="handleTabChange">
      <van-tabbar-item icon="home-o" name="home">
        Home
      </van-tabbar-item>
      <van-tabbar-item icon="user-o" name="profile">
        Profile
      </van-tabbar-item>
    </van-tabbar>

    <!-- 购物车弹窗 -->
    <van-popup
      v-model:show="showCartPopup"
      position="bottom"
      :style="{ height: '80%' }"
      round
      closeable
      close-icon="cross"
      @close="closeCartPopup"
    >
      <div class="cart-popup">
        <!-- 购物车头部 -->
        <div class="cart-header">
          <div class="cart-header-left">
            <h3 class="cart-title">Shopping Cart</h3>
          </div>
          <div class="cart-header-right">
            <button
              class="clear-btn"
              @click="clearCart"
              v-if="cartItems.length > 0"
            >
              Clear All
            </button>
          </div>
        </div>

        <!-- 购物车内容 -->
        <div class="cart-content">
          <!-- 空购物车状态 -->
          <div v-if="cartItems.length === 0" class="empty-cart">
            <van-icon name="shopping-cart-o" size="64" color="#cccccc" />
            <p class="empty-text">Your cart is empty</p>
            <p class="empty-subtext">Add some products to get started</p>
          </div>

          <!-- 购物车商品列表 -->
          <div v-else class="cart-items">
            <div
              v-for="item in cartItems"
              :key="item.id"
              class="cart-item"
            >
              <div class="cart-item-image">
                <img :src="item.image" :alt="item.name" />
                <div class="cart-item-badge" v-if="item.discount">
                  -{{ item.discount }}%
                </div>
              </div>
              <div class="cart-item-info">
                <div class="cart-item-content">
                  <h4 class="cart-item-name">{{ item.name }}</h4>
                  <p class="cart-item-description">{{ item.description }}</p>
                </div>
                <div class="cart-item-footer">
                  <div class="cart-item-left">
                    <div class="cart-item-price">
                      <span class="current-price">${{ item.price }}</span>
                      <span v-if="item.originalPrice" class="original-price">
                        ${{ item.originalPrice }}
                      </span>
                    </div>
                    <div class="cart-item-rating">
                      <van-icon name="star" color="#FFD700" size="12" />
                      <span class="rating-text">{{ item.rating }}</span>
                      <span class="rating-count">({{ item.reviews }})</span>
                    </div>
                  </div>
                  <div class="quantity-controls">
                    <button
                      class="quantity-btn decrease"
                      @click="decreaseQuantity(item)"
                    >
                      <van-icon name="minus" size="14" />
                    </button>
                    <span class="quantity-number">{{ item.quantity }}</span>
                    <button
                      class="quantity-btn increase"
                      @click="increaseQuantity(item)"
                    >
                      <van-icon name="plus" size="14" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分割线 -->
            <div class="divider"></div>

            <!-- 业务员选择 -->
            <div class="salesman-section">
              <div class="salesman-header">
                <h4 class="salesman-title">Salesman</h4>
                <button
                  v-if="selectedSalesman"
                  class="clear-salesman-btn"
                  @click="clearSalesmanSelection"
                >
                  Clear
                </button>
              </div>
              <div class="salesman-selector-wrapper" @click="showSalesmanPicker = true">
                <div class="salesman-display">
                  <van-icon name="contact" color="#7ed321" size="20" />
                  <span class="salesman-text">
                    {{ selectedSalesman ? selectedSalesman.name : 'Select Salesman' }}
                  </span>
                  <van-icon name="arrow-down" color="#999999" size="16" />
                </div>
              </div>
            </div>

            <!-- 支付方式选择 -->
            <div class="payment-section">
              <h4 class="payment-title">Payment Method</h4>
              <div class="payment-options">
                <div
                  class="payment-option"
                  :class="{ active: selectedPayment === 'all' }"
                  @click="selectPayment('all')"
                >
                  <div class="payment-left">
                    <van-icon name="credit-card" color="#7ed321" size="20" />
                    <div class="payment-info">
                      <span class="payment-name">All</span>
                      <span class="payment-desc">Pay with all methods</span>
                    </div>
                  </div>
                  <van-icon
                    v-if="selectedPayment === 'all'"
                    name="success"
                    color="#7ed321"
                    size="20"
                  />
                </div>

                <div
                  class="payment-option"
                  :class="{ active: selectedPayment === 'paypal' }"
                  @click="selectPayment('paypal')"
                >
                  <div class="payment-left">
                    <van-icon name="balance-pay" color="#003087" size="20" />
                    <div class="payment-info">
                      <span class="payment-name">Paypal</span>
                      <span class="payment-desc">Pay with Paypal</span>
                    </div>
                  </div>
                  <van-icon
                    v-if="selectedPayment === 'paypal'"
                    name="success"
                    color="#7ed321"
                    size="20"
                  />
                </div>

                <div
                  class="payment-option"
                  :class="{ active: selectedPayment === 'cashapp' }"
                  @click="selectPayment('cashapp')"
                >
                  <div class="payment-left">
                    <van-icon name="cash-back-record" color="#00d632" size="20" />
                    <div class="payment-info">
                      <span class="payment-name">Cash App</span>
                      <span class="payment-desc">Pay with Cash App</span>
                    </div>
                  </div>
                  <van-icon
                    v-if="selectedPayment === 'cashapp'"
                    name="success"
                    color="#7ed321"
                    size="20"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 购物车底部 -->
        <div v-if="cartItems.length > 0" class="cart-footer">
          <div class="cart-total">
            <span class="total-label">Total: </span>
            <span class="total-price">${{ cartTotal.toFixed(2) }}</span>
          </div>
          <button class="checkout-btn" @click="goToCheckout">
            Checkout ({{ cartCount }})
          </button>
        </div>
      </div>
    </van-popup>

    <!-- 业务员选择器弹窗 -->
    <van-popup
      v-model:show="showSalesmanPicker"
      position="bottom"
      :style="{ height: '60vh' }"
      round
      closeable
      close-icon="cross"
      @close="closeSalesmanPicker"
    >
      <div class="salesman-picker-popup">
        <div class="picker-header">
          <h3 class="picker-title">Select Salesman</h3>
          <div class="search-wrapper">
            <van-search
              v-model="salesmanSearchQuery"
              placeholder="Search by name or phone"
              :border="false"
              background="transparent"
            />
          </div>
        </div>
        <div class="salesman-list">
          <!-- 业务员列表 -->
          <div
            v-for="salesman in filteredSalesmanList"
            :key="salesman.id"
            class="salesman-item"
            :class="{ active: selectedSalesman?.id === salesman.id }"
            @click="selectSalesman(salesman)"
          >
            <div class="salesman-info">
              <div class="salesman-avatar">
                <van-icon name="contact" color="#7ed321" size="24" />
              </div>
              <div class="salesman-details">
                <span class="salesman-name">{{ salesman.name }}</span>
                <span class="salesman-phone">{{ salesman.phone }}</span>
              </div>
            </div>
            <van-icon
              v-if="selectedSalesman?.id === salesman.id"
              name="success"
              color="#7ed321"
              size="20"
            />
          </div>

          <!-- 空状态提示 -->
          <div v-if="filteredSalesmanList.length === 0" class="empty-salesman">
            <van-icon name="search" color="#999999" size="48" />
            <p class="empty-text">No matching salesman found</p>
            <p class="empty-desc">Try different search keywords</p>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { gsap } from 'gsap'
import { showConfirmDialog } from 'vant'
import { useRouter } from 'vue-router'
import configManager, { getConfig, isFeatureEnabled } from '@/utils/config'
import api from '@/utils/api'

const router = useRouter()

// 响应式数据
const isExpanded = ref(false)
const searchQuery = ref('')
const layoutMode = ref('grid') // 'grid' 或 'list'
const activeTab = ref('home') // 当前激活的tab
const cartCount = ref(0) // 购物车数量
const showCartPopup = ref(false) // 购物车弹窗显示状态
const cartItems = ref([]) // 购物车商品列表
const selectedPayment = ref('all') // 选中的支付方式
const selectedSalesman = ref(null) // 选中的业务员
const showSalesmanPicker = ref(false) // 业务员选择器显示状态
const salesmanSearchQuery = ref('') // 业务员搜索关键词
// 飞行的小球现在由GSAP动态创建，不需要响应式数组
const cartRef = ref(null) // 购物车元素引用

// 功能启用状态（响应式）
const cartEnabled = computed(() => isFeatureEnabled('cart_enabled'))
const paymentEnabled = computed(() => isFeatureEnabled('payment_enabled'))

// 食品分类数据
const categories = ref([])
const categoriesLoading = ref(false)

// 默认分类数据（作为备用，当接口失败时使用）
const defaultCategories = [
  { id: 'default-1', name: 'All Categories', icon: 'apps-o', color: '#7ed321', imageUrl: null },
  { id: 'default-2', name: 'Popular', icon: 'fire-o', color: '#ff6b6b', imageUrl: null },
  { id: 'default-3', name: 'New Arrivals', icon: 'star-o', color: '#4ecdc4', imageUrl: null }
]

// 商品数据
const products = ref([
  {
    id: 1,
    name: 'Fresh Organic Apples',
    description: 'Sweet and crispy organic apples',
    price: 4.99,
    originalPrice: 6.99,
    discount: 29,
    rating: 4.8,
    reviews: 124,
    image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=300&h=300&fit=crop'
  },
  {
    id: 2,
    name: 'Premium Avocados',
    description: 'Ripe and creamy avocados',
    price: 3.49,
    rating: 4.6,
    reviews: 89,
    image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=300&h=300&fit=crop'
  },
  {
    id: 3,
    name: 'Organic Bananas',
    description: 'Fresh yellow bananas',
    price: 2.99,
    originalPrice: 3.99,
    discount: 25,
    rating: 4.7,
    reviews: 156,
    image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=300&h=300&fit=crop'
  },
  {
    id: 4,
    name: 'Fresh Strawberries',
    description: 'Sweet and juicy strawberries',
    price: 5.99,
    rating: 4.9,
    reviews: 203,
    image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=300&h=300&fit=crop'
  }
])

// 业务员数据
const salesmanList = ref([
  {
    id: 1,
    name: '张小明',
    phone: '138-8888-8888'
  },
  {
    id: 2,
    name: '李小红',
    phone: '139-9999-9999'
  },
  {
    id: 3,
    name: '王小强',
    phone: '136-6666-6666'
  },
  {
    id: 4,
    name: '赵小美',
    phone: '137-7777-7777'
  },
  {
    id: 5,
    name: '陈小华',
    phone: '135-5555-5555'
  }
])

// 计算显示的分类
const visibleCategories = computed(() => {
  const totalCategories = categories.value.length

  if (totalCategories === 0) {
    return []
  }

  // 如果分类总数少于等于8个，直接显示所有分类（不需要展开/收缩）
  if (totalCategories <= 8) {
    return categories.value
  }

  // 如果分类总数大于8个，则需要展开/收缩功能
  if (isExpanded.value) {
    // 展开时显示所有分类
    return categories.value
  } else {
    // 收缩时显示前7个分类，为展开按钮留出位置
    return categories.value.slice(0, 7)
  }
})

// 计算是否需要显示展开/收缩按钮
const shouldShowExpandButton = computed(() => {
  return categories.value.length > 8
})

// 计算购物车总价
const cartTotal = computed(() => {
  return cartItems.value.reduce((total, item) => total + (item.price * item.quantity), 0)
})

// 过滤业务员列表
const filteredSalesmanList = computed(() => {
  if (!salesmanSearchQuery.value.trim()) {
    return salesmanList.value
  }

  const query = salesmanSearchQuery.value.toLowerCase().trim()
  return salesmanList.value.filter(salesman =>
    salesman.name.toLowerCase().includes(query) ||
    salesman.phone.includes(query)
  )
})

// 切换展开状态 (简单的状态切换，无动画)
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 设置布局模式
const setLayoutMode = (mode) => {
  layoutMode.value = mode
}

// 处理分类点击
const handleCategoryClick = (category) => {
  console.log('点击分类:', category.name)
  // 这里可以添加跳转到分类页面的逻辑
}

// 处理商品点击
const handleProductClick = (product) => {
  console.log('点击商品:', product.name)
  // 这里可以添加跳转到商品详情页面的逻辑
}

// 搜索功能
const onSearch = () => {
  console.log('搜索:', searchQuery.value)
  // 这里可以添加搜索逻辑
}

const onCancel = () => {
  searchQuery.value = ''
  console.log('取消搜索')
}

// 添加到购物车
const addToCart = (product, event) => {
  console.log('添加到购物车:', product.name)

  // 创建飞入动画
  try {
    createFlyingBall(event)
  } catch (error) {
    console.error('动画创建失败:', error)
  }

  // 检查商品是否已在购物车中
  const existingItem = cartItems.value.find(item => item.id === product.id)

  if (existingItem) {
    // 如果已存在，增加数量
    existingItem.quantity++
  } else {
    // 如果不存在，添加新商品
    cartItems.value.push({
      ...product,
      quantity: 1
    })
  }

  // 更新购物车总数量
  updateCartCount()
}

// 创建飞入小球动画 (使用GSAP)
const createFlyingBall = (event) => {
  console.log('开始创建飞入动画')

  const button = event.currentTarget
  const buttonRect = button.getBoundingClientRect()

  if (!cartRef.value) {
    console.error('购物车引用不存在')
    return
  }

  const cartRect = cartRef.value.getBoundingClientRect()

  // 计算起始和结束位置
  const startX = buttonRect.left + buttonRect.width / 2
  const startY = buttonRect.top + buttonRect.height / 2
  const endX = cartRect.left + cartRect.width / 2
  const endY = cartRect.top + cartRect.height / 2

  console.log('动画坐标:', { startX, startY, endX, endY })

  // 创建小球元素
  const ball = document.createElement('div')
  ball.className = 'flying-ball-gsap'
  ball.style.cssText = `
    position: fixed;
    left: ${startX}px;
    top: ${startY}px;
    width: 16px;
    height: 16px;
    background: #7ed321;
    border-radius: 50%;
    z-index: 999;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(126, 211, 33, 0.6);
    transform-origin: center center;
  `

  // 添加到页面
  document.body.appendChild(ball)

  // 计算抛物线参数
  const deltaX = endX - startX
  const deltaY = endY - startY
  const peakY = Math.min(startY, endY) - 120 // 抛物线最高点

  // 使用GSAP创建抛物线动画
  const tl = gsap.timeline({
    onComplete: () => {
      // 动画完成后移除小球
      if (document.body.contains(ball)) {
        document.body.removeChild(ball)
      }

      // 添加购物车震动效果
      gsap.to(cartRef.value, {
        scale: 1.15,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "back.out(1.7)"
      })
    }
  })

  // 水平移动 (线性)
  tl.to(ball, {
    x: deltaX,
    duration: 0.8,
    ease: "none"
  })

  // 垂直移动 (抛物线 - 先上后下)
  tl.to(ball, {
    y: peakY - startY,
    duration: 0.4,
    ease: "power2.out"
  }, 0)

  tl.to(ball, {
    y: deltaY,
    duration: 0.4,
    ease: "power2.in"
  }, 0.4)

  // 移除小球缩放效果，保持原始大小

  // 旋转效果
  tl.to(ball, {
    rotation: 360,
    duration: 0.8,
    ease: "none"
  }, 0)
}



// 更新购物车总数量
const updateCartCount = () => {
  cartCount.value = cartItems.value.reduce((total, item) => total + item.quantity, 0)
}

// 打开购物车弹窗
const goToCart = () => {
  console.log('打开购物车弹窗')
  showCartPopup.value = true
}

// 关闭购物车弹窗
const closeCartPopup = () => {
  showCartPopup.value = false
}

// 清空购物车
const clearCart = () => {
  console.log('请求清空购物车')

  showConfirmDialog({
    title: 'Clear Cart',
    message: 'Are you sure you want to remove all items from your cart?',
    confirmButtonText: 'Clear All',
    cancelButtonText: 'Cancel',
    confirmButtonColor: '#e74c3c',
  })
  .then(() => {
    // 用户确认清空
    console.log('确认清空购物车')
    cartItems.value = []
    updateCartCount()
  })
  .catch(() => {
    // 用户取消
    console.log('取消清空购物车')
  })
}

// 增加商品数量
const increaseQuantity = (item) => {
  item.quantity++
  updateCartCount()
}

// 减少商品数量
const decreaseQuantity = (item) => {
  if (item.quantity > 1) {
    item.quantity--
  } else {
    // 如果数量为1，则从购物车中移除
    const index = cartItems.value.findIndex(cartItem => cartItem.id === item.id)
    if (index > -1) {
      cartItems.value.splice(index, 1)
    }
  }
  updateCartCount()
}

// 选择支付方式
const selectPayment = (method) => {
  selectedPayment.value = method
  console.log('选择支付方式:', method)
}

// 选择业务员
const selectSalesman = (salesman) => {
  selectedSalesman.value = salesman
  showSalesmanPicker.value = false
  salesmanSearchQuery.value = '' // 清空搜索
  console.log('选择业务员:', salesman.name)
}

// 关闭业务员选择器时重置搜索
const closeSalesmanPicker = () => {
  showSalesmanPicker.value = false
  salesmanSearchQuery.value = ''
}

// 刷新分类数据
const refreshCategories = async () => {
  console.log('手动刷新分类数据')
  await loadCategories()
}

// 跳转到订单详情页面（从购物车）
const goToCheckout = () => {
  if (cartItems.value.length === 0) {
    return
  }

  // 检查支付功能是否启用
  if (!paymentEnabled.value) {
    console.warn('支付功能未启用')
    return
  }

  // 准备订单数据
  const orderData = {
    orderNumber: '', // 新订单暂时没有订单号
    date: new Date().toISOString().split('T')[0],
    items: cartItems.value.map(item => ({
      ...item,
      description: item.description || '新鲜优质商品'
    })),
    total: cartTotal.value
  }

  // 准备业务员信息
  const salesmanData = selectedSalesman.value || {
    name: 'John Smith',
    phone: '+****************'
  }

  // 关闭购物车弹窗
  showCartPopup.value = false

  // 跳转到订单详情页面
  router.push({
    name: 'OrderDetail',
    query: {
      orderData: JSON.stringify(orderData),
      isFromCart: 'true',
      paymentMethod: selectedPayment.value,
      salesmanInfo: JSON.stringify(salesmanData)
    }
  })
}

// 加载分类数据
const loadCategories = async () => {
  categoriesLoading.value = true

  try {
    console.log('正在加载分类数据...')
    const response = await api.loadSorts()

    if (response && Array.isArray(response)) {
      // 处理服务器返回的分类数据
      categories.value = response.map(category => {
        // 构建分类图片URL
        let imageUrl = null
        if (category.sort_icon) {
          // 拼接完整的图片URL
          imageUrl = `http://206.233.245.198${category.sort_icon}`
        }

        return {
          id: category.id,
          name: category.name || category.title || category.sort_name,
          icon: category.icon || 'apps-o',
          color: category.color || '#7ed321',
          imageUrl: imageUrl,
          sort_icon: category.sort_icon // 保留原始字段用于调试
        }
      })
      console.log('分类数据加载成功:', categories.value)
      console.log(`共加载 ${categories.value.length} 个分类`)
    } else if (response && response.data && Array.isArray(response.data)) {
      // 如果数据在data字段中
      categories.value = response.data.map(category => {
        // 构建分类图片URL
        let imageUrl = null
        if (category.sort_icon) {
          // 拼接完整的图片URL
          imageUrl = `http://206.233.245.198${category.sort_icon}`
        }

        return {
          id: category.id,
          name: category.name || category.title || category.sort_name,
          icon: category.icon || 'apps-o',
          color: category.color || '#7ed321',
          imageUrl: imageUrl,
          sort_icon: category.sort_icon // 保留原始字段用于调试
        }
      })
      console.log('分类数据加载成功:', categories.value)
      console.log(`共加载 ${categories.value.length} 个分类`)
    } else {
      console.warn('分类数据格式不正确，使用默认分类')
      categories.value = defaultCategories
    }
  } catch (error) {
    console.error('加载分类数据失败:', error)
    console.log('使用默认分类数据')
    categories.value = defaultCategories
  } finally {
    categoriesLoading.value = false
  }
}

// 组件挂载时的初始化
onMounted(async () => {
  // 加载分类数据
  await loadCategories()

  // 检查配置状态
  const configStatus = configManager.getStatus()
  console.log('Home页面配置状态:', configStatus)

  // 检查功能是否启用
  console.log('功能状态:', {
    cartEnabled: cartEnabled.value,
    paymentEnabled: paymentEnabled.value
  })

  // 获取UI配置
  const appName = getConfig('app_name', 'US Shop')
  const currency = getConfig('ui.currency', 'USD')

  console.log('UI配置:', {
    appName,
    currency
  })

  // 开发环境下添加分类调试方法
  if (import.meta.env.DEV) {
    window.categoryDebug = {
      getCategories: () => {
        console.log('所有分类数据:', categories.value)
        console.log('可见分类数据:', visibleCategories.value)
        console.log('是否展开:', isExpanded.value)
        console.log('是否显示展开按钮:', shouldShowExpandButton.value)
        return {
          total: categories.value.length,
          visible: visibleCategories.value.length,
          expanded: isExpanded.value,
          showExpandButton: shouldShowExpandButton.value
        }
      },
      refreshCategories: () => loadCategories(),
      toggleExpand: () => toggleExpand(),
      checkCart: () => {
        console.log('购物车引用:', cartRef.value)
        console.log('购物车启用状态:', cartEnabled.value)
        console.log('购物车数量:', cartCount.value)
        return {
          cartRef: !!cartRef.value,
          cartEnabled: cartEnabled.value,
          cartCount: cartCount.value
        }
      }
    }
    console.log('分类调试工具已加载，使用 categoryDebug.checkCart() 检查购物车状态')
  }
})

// 清除业务员选择
const clearSalesmanSelection = () => {
  selectedSalesman.value = null
  console.log('清除业务员选择')
}

// 处理底部Tab切换
const handleTabChange = (name) => {
  if (name === 'profile') {
    router.push('/profile')
  }
}
</script>

<style scoped>
/* 首页容器 */
.home-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px 16px 80px; /* 底部留出TabBar空间 */
}

/* 分类区域 */
.category-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 区域标题 */
.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

/* 分类网格容器 - 简单的展开/收缩，无动画 */

/* 分类加载状态 */
.categories-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  min-height: 120px;
}

/* 分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  /* GSAP会动态控制height属性 */
}

/* 飞入动画小球现在由GSAP处理，不需要CSS动画 */

/* 浮动购物车 */
.floating-cart {
  position: fixed;
  bottom: 85px; /* 在TabBar之上，稍微向下偏移 */
  right: 20px;
  z-index: 1000;
  cursor: pointer;
}

.cart-icon {
  position: relative;
  width: 56px;
  height: 56px;
  background: #7ed321;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 16px rgba(126, 211, 33, 0.4),
    0 2px 8px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.cart-icon:hover {
  background: #6bc91a;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(126, 211, 33, 0.5);
}

.cart-icon:active {
  transform: scale(0.95);
}



.cart-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid white;
}

/* 底部TabBar */
.custom-tabbar {
  --van-tabbar-background: #ffffff;
  --van-tabbar-item-text-color: #999999;
  --van-tabbar-item-active-color: #7ed321;
  --van-tabbar-item-active-background: transparent;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.van-tabbar-item__icon) {
  font-size: 20px;
}

:deep(.van-tabbar-item__text) {
  font-size: 12px;
  font-weight: 500;
}

/* 分类网格样式已在上面定义 */

/* 分类项 */
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease; /* 恢复正常的过渡效果 */
  background-color: #f8f9fa;
}

.category-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
}

.category-item:active {
  transform: translateY(0);
}

/* 分类图标 */
.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  overflow: hidden; /* 确保图片不会超出圆形容器 */
}

/* 分类图片 */
.category-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
}

.category-item:hover .category-icon {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 展开按钮特殊样式 */
.expand-item .category-icon {
  background-color: rgba(126, 211, 33, 0.1);
  border: 2px solid rgba(126, 211, 33, 0.2);
}

.expand-icon {
  background-color: rgba(126, 211, 33, 0.1) !important;
}

/* 分类名称 */
.category-name {
  font-size: 12px;
  color: #666666;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

.expand-item .category-name {
  color: #7ed321;
  font-weight: 600;
}

/* 搜索栏区域 */
.search-section {
  margin: 16px 0;
}

.search-container {
  background: #ffffff;
  border-radius: 12px;
  padding: 4px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.custom-search {
  --van-search-background: transparent;
  --van-search-content-background: #f8f9fa;
  --van-search-input-height: 44px;
  --van-field-input-text-color: #1a1a1a;
  --van-field-placeholder-text-color: #999999;
}

:deep(.custom-search .van-search__content) {
  border-radius: 22px;
  border: 1px solid #e5e5e5;
  transition: all 0.3s ease;
}

:deep(.custom-search .van-search__content:focus-within) {
  border-color: #7ed321;
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(126, 211, 33, 0.1);
}

.search-action {
  color: #7ed321;
  font-weight: 600;
  font-size: 14px;
  padding: 0 8px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.search-action:hover {
  color: #6bc91a;
}

/* 商品列表区域 */
.products-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.products-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.layout-toggle {
  display: flex;
  gap: 12px;
}

.layout-icon {
  padding: 8px;
  border-radius: 6px;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.layout-icon:hover {
  background-color: #e9ecef;
}

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  transition: all 0.3s ease;
}

.products-grid.list-mode {
  grid-template-columns: 1fr;
}

/* 商品项 */
.product-item {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #7ed321;
}

.list-mode .product-item {
  display: flex;
  flex-direction: row;
  height: 140px; /* 增加列表项高度以容纳所有内容 */
}

.list-mode .product-item .product-image {
  width: 140px; /* 增加图片宽度以匹配新的高度 */
  height: 100%; /* 使用100%高度与父容器保持一致 */
  flex-shrink: 0;
  border-radius: 12px 0 0 12px; /* 左侧圆角 */
  overflow: hidden; /* 确保图片不会超出容器 */
}

.list-mode .product-item .product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; /* 确保图片居中显示 */
  display: block; /* 移除img标签默认的inline样式 */
}

.list-mode .product-item .product-info {
  flex: 1;
  padding: 12px 16px; /* 减少上下内边距 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 商品图片 */
.product-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center; /* 确保图片居中显示 */
  display: block; /* 移除img标签默认的inline样式，避免底部空隙 */
  transition: transform 0.3s ease;
}

.product-item:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 商品信息 */
.product-info {
  padding: 12px;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 2px 0; /* 减少底部间距 */
  line-height: 1.2; /* 减少行高 */
}

.product-description {
  font-size: 11px; /* 减小字体大小 */
  color: #666666;
  margin: 0 0 4px 0; /* 添加底部间距 */
  line-height: 1.3; /* 减少行高 */
  flex: 1;
  overflow: hidden; /* 防止文本溢出 */
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制为最多2行 */
  -webkit-box-orient: vertical;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 4px; /* 减少顶部间距 */
}

.product-left {
  display: flex;
  flex-direction: column;
  gap: 2px; /* 减少间距 */
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-to-cart-btn {
  width: 28px;
  height: 28px;
  background: #7ed321;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(126, 211, 33, 0.3);
}

.add-to-cart-btn:hover {
  background: #6bc91a;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.4);
}

.add-to-cart-btn:active {
  transform: scale(0.95);
}

.current-price {
  font-size: 16px;
  font-weight: 700;
  color: #7ed321;
}

.original-price {
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-text {
  font-size: 12px;
  font-weight: 600;
  color: #1a1a1a;
}

.rating-count {
  font-size: 11px;
  color: #999999;
}

/* 列表模式下的特定样式优化 */
.list-mode .product-name {
  font-size: 13px; /* 列表模式下稍小的标题字体 */
  margin: 0 0 2px 0;
  line-height: 1.2;
}

.list-mode .product-description {
  font-size: 10px; /* 列表模式下更小的描述字体 */
  line-height: 1.2;
  margin: 0 0 2px 0;
}

.list-mode .current-price {
  font-size: 14px; /* 列表模式下稍小的价格字体 */
}

.list-mode .rating-text {
  font-size: 11px; /* 列表模式下稍小的评分字体 */
}

.list-mode .rating-count {
  font-size: 10px; /* 列表模式下更小的评论数字体 */
}

.list-mode .add-to-cart-btn {
  width: 26px; /* 列表模式下稍小的按钮 */
  height: 26px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .home-container {
    padding: 16px 12px 80px; /* 移动端也要为TabBar留出空间 */
  }

  /* .category-section, */
  /* .search-section .search-container, */
  /* .products-section {
    padding: 16px;
  } */

  .section-title,
  .products-title {
    font-size: 16px;
  }

  /* 移动端分类容器无需高度限制 */

  .category-grid {
    gap: 12px;
  }

  .category-item {
    padding: 10px 6px;
  }

  .category-icon {
    width: 40px;
    height: 40px;
  }

  /* 移动端分类图片 */
  .category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 50%;
  }

  .category-name {
    font-size: 11px;
  }

  .products-grid {
    gap: 12px;
  }

  .product-image {
    height: 140px;
  }

  .list-mode .product-item {
    height: 120px; /* 增加移动端列表项高度 */
  }

  .list-mode .product-item .product-image {
    width: 120px; /* 增加移动端图片宽度 */
    height: 100%; /* 使用100%高度与父容器保持一致 */
    border-radius: 8px 0 0 8px; /* 移动端稍小的圆角 */
    overflow: hidden;
  }

  .list-mode .product-item .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
  }

  .list-mode .product-item .product-info {
    padding: 10px 12px; /* 减少移动端上下内边距 */
  }

  .layout-toggle {
    gap: 8px;
  }

  .layout-icon {
    padding: 6px;
  }

  /* 移动端飞入动画现在由GSAP处理 */

  /* 移动端浮动购物车 */
  .floating-cart {
    bottom: 70px; /* 移动端调整位置，稍微向下偏移 */
    right: 16px;
  }

  .cart-icon {
    width: 50px;
    height: 50px;
  }

  .cart-badge {
    width: 18px;
    height: 18px;
    font-size: 11px;
  }
}

/* 购物车弹窗样式 */
.cart-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 60px 16px 20px; /* 右侧留出更多空间给关闭按钮 */
  background: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  position: relative;
  height: 56px; /* 固定高度而不是最小高度 */
  flex-shrink: 0; /* 防止被压缩 */
}

.cart-header-left {
  display: flex;
  align-items: center;
}

.cart-header-right {
  display: flex;
  align-items: center;
  min-width: 80px; /* 确保右侧区域始终占用固定宽度 */
  justify-content: flex-end; /* 按钮靠右对齐 */
}

.cart-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  line-height: 1.2; /* 控制行高 */
}

.clear-btn {
  background: none;
  border: none;
  color: #7ed321;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  height: 32px;
  display: flex;
  align-items: center;
  white-space: nowrap; /* 防止文字换行 */
}

.clear-btn:hover {
  background-color: rgba(126, 211, 33, 0.1);
}

/* 调整Vant Popup关闭按钮的样式 */
:deep(.van-popup__close-icon) {
  top: 16px !important; /* 与标题栏对齐 */
  right: 16px !important;
  z-index: 10; /* 确保在最上层 */
}

.cart-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* 空购物车状态 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.empty-text {
  font-size: 18px;
  font-weight: 600;
  color: #666666;
  margin: 16px 0 8px 0;
}

.empty-subtext {
  font-size: 14px;
  color: #999999;
  margin: 0;
}

/* 购物车商品列表 */
.cart-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.cart-item {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  height: 140px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.cart-item-image {
  position: relative;
  width: 140px;
  height: 100%;
  flex-shrink: 0;
  overflow: hidden;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.cart-item-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.cart-item-info {
  flex: 1;
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cart-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cart-item-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 2px 0;
  line-height: 1.2;
}

.cart-item-description {
  font-size: 11px;
  color: #666666;
  margin: 0 0 4px 0;
  line-height: 1.3;
  flex: 1;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.cart-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 4px;
}

.cart-item-left {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cart-item-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.cart-item-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 数量控制器 */
.quantity-controls {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 20px;
  padding: 4px;
  gap: 8px;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #7ed321;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(126, 211, 33, 0.3);
}

.quantity-btn:hover {
  background: #6bc91a;
  transform: scale(1.05);
}

.quantity-btn:active {
  transform: scale(0.95);
}

.quantity-btn.decrease {
  background: #ff6b6b;
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
}

.quantity-btn.decrease:hover {
  background: #ff5252;
}

.quantity-number {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  min-width: 20px;
  text-align: center;
}

/* 购物车底部 */
.cart-footer {
  background: #ffffff;
  padding: 20px;
  border-top: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 分割线 */
.divider {
  height: 1px;
  background: #e5e5e5;
  margin: 8px 0;
}

/* 业务员选择区域 */
.salesman-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.salesman-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.salesman-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.clear-salesman-btn {
  background: none;
  border: 1px solid #e74c3c;
  color: #e74c3c;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  height: 24px;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.clear-salesman-btn:hover {
  background-color: #e74c3c;
  color: white;
}

.salesman-selector-wrapper {
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  overflow: hidden;
  transition: border-color 0.3s ease;
  min-height: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  cursor: pointer;
  background: #ffffff;
}

.salesman-selector-wrapper:hover {
  border-color: #7ed321;
}

.salesman-display {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 16px;
  gap: 12px;
}

.salesman-text {
  flex: 1;
  font-size: 14px;
  color: #1a1a1a;
}

/* 业务员选择器弹窗样式 */
.salesman-picker-popup {
  background: #ffffff;
  border-radius: 16px 16px 0 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.picker-header {
  padding: 20px 16px 16px;
  border-bottom: 1px solid #e5e5e5;
}

.picker-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
  text-align: center;
}

.search-wrapper {
  margin-top: 8px;
}

:deep(.search-wrapper .van-search) {
  padding: 0;
  background: #f8f9fa;
  border-radius: 8px;
}

:deep(.search-wrapper .van-search__content) {
  background: #f8f9fa;
  border-radius: 8px;
}

:deep(.search-wrapper .van-field__control) {
  font-size: 14px;
  color: #1a1a1a;
}

.salesman-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

.salesman-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.salesman-item:hover {
  background: rgba(126, 211, 33, 0.1);
}

.salesman-item.active {
  background: rgba(126, 211, 33, 0.1);
  border: 2px solid #7ed321;
}

.salesman-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.salesman-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(126, 211, 33, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.salesman-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.salesman-name {
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
}

.salesman-phone {
  font-size: 14px;
  color: #666666;
}

/* 空状态样式 */
.empty-salesman {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-text {
  font-size: 16px;
  color: #666666;
  margin: 16px 0 8px 0;
  font-weight: 500;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
  margin: 0;
}



:deep(.address-input .van-field) {
  min-height: 44px !important;
  height: 44px !important;
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
  position: relative !important;
}

/* 额外的样式来确保完全填充 */
:deep(.address-input .van-field__left-icon),
:deep(.address-input .van-field__right-icon) {
  display: none !important;
}

:deep(.address-input .van-cell__value) {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* 支付方式选择 */
.payment-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.payment-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.payment-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-option:hover {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.05);
}

.payment-option.active {
  border-color: #7ed321;
  background: rgba(126, 211, 33, 0.1);
}

.payment-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.payment-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

.payment-desc {
  font-size: 12px;
  color: #666666;
}

.cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-label {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.total-price {
  font-size: 20px;
  font-weight: 700;
  color: #7ed321;
}

.checkout-btn {
  width: 100%;
  height: 48px;
  background: #7ed321;
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.3);
}

.checkout-btn:hover {
  background: #6bc91a;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(126, 211, 33, 0.4);
}

.checkout-btn:active {
  transform: translateY(0);
}
</style>
