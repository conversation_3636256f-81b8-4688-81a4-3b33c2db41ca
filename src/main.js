import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { Button, Field, Form, Icon, Search, Tabbar, TabbarItem, Popup, Dialog, Picker, Tabs, Tab, DatePicker, Toast, Loading } from 'vant'
import 'vant/lib/index.css'
import './style.css'
import App from './App.vue'
import Login from './views/Login.vue'
import Home from './views/Home.vue'
import Profile from './views/Profile.vue'
import OrderDetail from './views/OrderDetail.vue'

import api from './utils/api'
import configManager, { initConfig } from './utils/config'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Login',
    component: Login
  },
  {
    path: '/home',
    name: 'Home',
    component: Home
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile
  },
  {
    path: '/order-detail',
    name: 'OrderDetail',
    component: OrderDetail
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 首先初始化配置（不管是否登录都需要加载配置）
  try {
    console.log('正在初始化应用配置...')
    await initConfig()
    console.log('应用配置初始化完成')
  } catch (error) {
    console.error('配置初始化失败:', error)
    // 配置加载失败不阻塞路由，使用默认配置继续
  }

  const isLoggedIn = api.isLoggedIn()

  console.log('路由守卫:', {
    to: to.path,
    from: from.path,
    isLoggedIn,
    configStatus: configManager.getStatus()
  })

  // 如果访问登录页
  if (to.path === '/') {
    if (isLoggedIn) {
      // 已登录，跳转到首页
      next('/home')
    } else {
      // 未登录，正常访问登录页
      next()
    }
  }
  // 如果访问需要登录的页面
  else if (to.path === '/home' || to.path === '/profile' || to.path === '/order-detail') {
    if (isLoggedIn) {
      // 已登录，正常访问
      next()
    } else {
      // 未登录，跳转到登录页
      console.log('未登录，跳转到登录页')
      next('/')
    }
  }
  // 其他页面
  else {
    next()
  }
})

const app = createApp(App)

// 注册Vant组件
app.use(Button)
app.use(Field)
app.use(Form)
app.use(Icon)
app.use(Search)
app.use(Tabbar)
app.use(TabbarItem)
app.use(Popup)
app.use(Dialog)
app.use(Picker)
app.use(Tabs)
app.use(Tab)
app.use(DatePicker)
app.use(Toast)
app.use(Loading)
app.use(router)

app.mount('#app')

// 开发环境下暴露API测试方法
if (import.meta.env.DEV) {
  window.apiDebug = {
    testHeaders: () => api.testHeaders(),
    getUserInfo: () => api.getUserInfo(),
    getToken: () => api.getToken(),
    isLoggedIn: () => api.isLoggedIn()
  }
  console.log('API调试工具已加载，使用 apiDebug.testHeaders() 测试请求头')
}


