import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    proxy: {
      // 代理所有以 /api 开头的请求到目标服务器
      '/api': {
        target: 'http://206.233.245.198/LoadData',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.log('代理错误:', err.message)
          })
          proxy.on('proxyReq', (proxyReq, req) => {
            console.log('发送请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req) => {
            console.log('收到响应:', proxyRes.statusCode, req.url)
          })
        }
      }
    }
  }
})
